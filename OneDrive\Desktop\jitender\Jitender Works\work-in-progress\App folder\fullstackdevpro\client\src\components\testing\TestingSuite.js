import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';

const SuiteContainer = styled.div`
  padding: 2rem;
  background: ${props => props.theme.background};
  height: 100vh;
  overflow: auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: between;
  align-items: center;
  margin-bottom: 2rem;
  
  h1 {
    margin: 0;
    color: ${props => props.theme.text};
  }
`;

const TabContainer = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 2rem;
  border-bottom: 1px solid ${props => props.theme.divider};
`;

const Tab = styled.button`
  padding: 0.75rem 1.5rem;
  background: ${props => props.active ? props.theme.primary : 'transparent'};
  color: ${props => props.active ? 'white' : props.theme.text};
  border: none;
  border-bottom: 2px solid ${props => props.active ? props.theme.primary : 'transparent'};
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: ${props => props.active ? props.theme.primaryDark : props.theme.hover};
  }
`;

const TestGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  height: calc(100vh - 200px);
`;

const TestPanel = styled.div`
  background: ${props => props.theme.surface};
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: ${props => props.theme.shadows.medium};
  overflow: auto;
`;

const TestList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const TestItem = styled(motion.div)`
  padding: 1rem;
  background: ${props => props.theme.card.background};
  border: 1px solid ${props => props.theme.divider};
  border-left: 4px solid ${props => getStatusColor(props.status)};
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    transform: translateX(4px);
    box-shadow: ${props => props.theme.shadows.small};
  }

  .test-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  .test-name {
    font-weight: 500;
    color: ${props => props.theme.text};
  }

  .test-status {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    background: ${props => getStatusColor(props.status)};
    color: white;
  }

  .test-description {
    color: ${props => props.theme.textSecondary};
    font-size: 0.875rem;
  }
`;

const ResultsPanel = styled.div`
  background: ${props => props.theme.surface};
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: ${props => props.theme.shadows.medium};
  overflow: auto;
`;

const TestOutput = styled.pre`
  background: ${props => props.theme.code.background};
  color: ${props => props.theme.code.text};
  padding: 1rem;
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  overflow: auto;
  max-height: 300px;
`;

const ActionBar = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
`;

const Button = styled.button`
  padding: 0.5rem 1rem;
  background: ${props => props.primary ? props.theme.primary : props.theme.surface};
  color: ${props => props.primary ? 'white' : props.theme.text};
  border: 1px solid ${props => props.primary ? 'transparent' : props.theme.divider};
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: ${props => props.primary ? props.theme.primaryDark : props.theme.hover};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const StatsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 1rem;
  margin-bottom: 2rem;
`;

const StatCard = styled.div`
  background: ${props => props.theme.surface};
  padding: 1rem;
  border-radius: 8px;
  text-align: center;
  box-shadow: ${props => props.theme.shadows.small};

  .stat-value {
    font-size: 2rem;
    font-weight: bold;
    color: ${props => props.color || props.theme.primary};
  }

  .stat-label {
    color: ${props => props.theme.textSecondary};
    font-size: 0.875rem;
  }
`;

const getStatusColor = (status) => {
  const colors = {
    passed: '#4CAF50',
    failed: '#F44336',
    running: '#2196F3',
    pending: '#FFC107',
    skipped: '#757575'
  };
  return colors[status] || '#757575';
};

const TestingSuite = () => {
  const [activeTab, setActiveTab] = useState('unit');
  const [tests, setTests] = useState([]);
  const [selectedTest, setSelectedTest] = useState(null);
  const [isRunning, setIsRunning] = useState(false);
  const [testResults, setTestResults] = useState('');
  const [stats, setStats] = useState({
    total: 0,
    passed: 0,
    failed: 0,
    coverage: 0
  });

  useEffect(() => {
    loadTests(activeTab);
  }, [activeTab]);

  const loadTests = (testType) => {
    // Simulate loading different types of tests
    const mockTests = {
      unit: [
        {
          id: 1,
          name: 'Button Component Tests',
          description: 'Tests for button component functionality',
          status: 'passed',
          file: 'Button.test.js',
          duration: '0.5s'
        },
        {
          id: 2,
          name: 'Form Validation Tests',
          description: 'Tests for form validation logic',
          status: 'failed',
          file: 'FormValidation.test.js',
          duration: '1.2s'
        },
        {
          id: 3,
          name: 'API Service Tests',
          description: 'Tests for API service methods',
          status: 'passed',
          file: 'ApiService.test.js',
          duration: '0.8s'
        }
      ],
      integration: [
        {
          id: 4,
          name: 'User Authentication Flow',
          description: 'End-to-end authentication testing',
          status: 'passed',
          file: 'auth.integration.test.js',
          duration: '3.2s'
        },
        {
          id: 5,
          name: 'Database Operations',
          description: 'Database CRUD operations testing',
          status: 'running',
          file: 'database.integration.test.js',
          duration: '2.1s'
        }
      ],
      e2e: [
        {
          id: 6,
          name: 'User Registration Journey',
          description: 'Complete user registration flow',
          status: 'passed',
          file: 'registration.e2e.js',
          duration: '15.3s'
        },
        {
          id: 7,
          name: 'Project Creation Workflow',
          description: 'Creating and managing projects',
          status: 'pending',
          file: 'project-creation.e2e.js',
          duration: '12.7s'
        }
      ]
    };

    setTests(mockTests[testType] || []);
    updateStats(mockTests[testType] || []);
  };

  const updateStats = (testList) => {
    const total = testList.length;
    const passed = testList.filter(t => t.status === 'passed').length;
    const failed = testList.filter(t => t.status === 'failed').length;
    const coverage = Math.round((passed / total) * 100) || 0;

    setStats({ total, passed, failed, coverage });
  };

  const runAllTests = async () => {
    setIsRunning(true);
    setTestResults('Starting test suite...\n');

    // Simulate test execution
    for (let i = 0; i < tests.length; i++) {
      const test = tests[i];
      setTestResults(prev => prev + `Running ${test.name}...\n`);
      
      // Simulate test duration
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      const result = Math.random() > 0.3 ? 'PASS' : 'FAIL';
      setTestResults(prev => prev + `${result}: ${test.name} (${test.duration})\n`);
      
      // Update test status
      setTests(prevTests => 
        prevTests.map(t => 
          t.id === test.id 
            ? { ...t, status: result === 'PASS' ? 'passed' : 'failed' }
            : t
        )
      );
    }

    setTestResults(prev => prev + '\nTest suite completed!\n');
    setIsRunning(false);
  };

  const runSingleTest = async (test) => {
    setSelectedTest(test);
    setIsRunning(true);
    setTestResults(`Running ${test.name}...\n`);

    // Simulate single test execution
    await new Promise(resolve => setTimeout(resolve, 2000));

    const mockOutput = `
✓ ${test.name}
  ✓ should render correctly
  ✓ should handle click events
  ✓ should apply correct styles
  ✓ should be accessible

Test Suites: 1 passed, 1 total
Tests: 4 passed, 4 total
Snapshots: 0 total
Time: ${test.duration}
Ran all test suites.
    `;

    setTestResults(mockOutput);
    setIsRunning(false);
  };

  const generateTestReport = () => {
    const report = {
      timestamp: new Date().toISOString(),
      stats,
      tests: tests.map(t => ({
        name: t.name,
        status: t.status,
        duration: t.duration
      }))
    };

    const blob = new Blob([JSON.stringify(report, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `test-report-${Date.now()}.json`;
    a.click();
  };

  return (
    <SuiteContainer>
      <Header>
        <h1>Testing Suite</h1>
      </Header>

      <StatsContainer>
        <StatCard>
          <div className="stat-value">{stats.total}</div>
          <div className="stat-label">Total Tests</div>
        </StatCard>
        <StatCard color="#4CAF50">
          <div className="stat-value">{stats.passed}</div>
          <div className="stat-label">Passed</div>
        </StatCard>
        <StatCard color="#F44336">
          <div className="stat-value">{stats.failed}</div>
          <div className="stat-label">Failed</div>
        </StatCard>
        <StatCard color="#2196F3">
          <div className="stat-value">{stats.coverage}%</div>
          <div className="stat-label">Coverage</div>
        </StatCard>
      </StatsContainer>

      <TabContainer>
        <Tab active={activeTab === 'unit'} onClick={() => setActiveTab('unit')}>
          Unit Tests
        </Tab>
        <Tab active={activeTab === 'integration'} onClick={() => setActiveTab('integration')}>
          Integration Tests
        </Tab>
        <Tab active={activeTab === 'e2e'} onClick={() => setActiveTab('e2e')}>
          E2E Tests
        </Tab>
      </TabContainer>

      <ActionBar>
        <Button primary onClick={runAllTests} disabled={isRunning}>
          {isRunning ? 'Running...' : 'Run All Tests'}
        </Button>
        <Button onClick={generateTestReport}>
          Generate Report
        </Button>
        <Button>
          Configure Tests
        </Button>
      </ActionBar>

      <TestGrid>
        <TestPanel>
          <h3>Tests</h3>
          <TestList>
            <AnimatePresence>
              {tests.map(test => (
                <TestItem
                  key={test.id}
                  status={test.status}
                  onClick={() => runSingleTest(test)}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                >
                  <div className="test-header">
                    <span className="test-name">{test.name}</span>
                    <span className="test-status">{test.status}</span>
                  </div>
                  <div className="test-description">{test.description}</div>
                  <div style={{ fontSize: '0.75rem', color: '#666', marginTop: '0.5rem' }}>
                    {test.file} • {test.duration}
                  </div>
                </TestItem>
              ))}
            </AnimatePresence>
          </TestList>
        </TestPanel>

        <ResultsPanel>
          <h3>Test Results</h3>
          {selectedTest && (
            <div style={{ marginBottom: '1rem', padding: '0.5rem', background: '#f5f5f5', borderRadius: '4px' }}>
              Running: {selectedTest.name}
            </div>
          )}
          <TestOutput>{testResults || 'Select a test to see results...'}</TestOutput>
        </ResultsPanel>
      </TestGrid>
    </SuiteContainer>
  );
};

export default TestingSuite;
