import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';

const HubContainer = styled.div`
  display: grid;
  grid-template-columns: 300px 1fr 300px;
  height: 100vh;
  background: ${props => props.theme.background};
`;

const Sidebar = styled.div`
  background: ${props => props.theme.surface};
  border-right: 1px solid ${props => props.theme.divider};
  padding: 1rem;
  overflow-y: auto;
`;

const MainArea = styled.div`
  display: flex;
  flex-direction: column;
  background: ${props => props.theme.background};
`;

const ChatPanel = styled.div`
  background: ${props => props.theme.surface};
  border-left: 1px solid ${props => props.theme.divider};
  display: flex;
  flex-direction: column;
`;

const UserList = styled.div`
  margin-bottom: 2rem;

  h3 {
    margin-bottom: 1rem;
    color: ${props => props.theme.text};
  }
`;

const UserItem = styled(motion.div)`
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.5rem;
  border-radius: 4px;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: background 0.2s;

  &:hover {
    background: ${props => props.theme.hover};
  }
`;

const UserAvatar = styled.div`
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: ${props => props.color || props.theme.primary};
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 0.875rem;
`;

const UserInfo = styled.div`
  flex: 1;

  .name {
    font-weight: 500;
    color: ${props => props.theme.text};
  }

  .status {
    font-size: 0.75rem;
    color: ${props => props.theme.textSecondary};
  }
`;

const StatusIndicator = styled.div`
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: ${props => props.online ? '#4CAF50' : '#757575'};
`;

const ProjectActivity = styled.div`
  h3 {
    margin-bottom: 1rem;
    color: ${props => props.theme.text};
  }
`;

const ActivityItem = styled(motion.div)`
  padding: 0.75rem;
  background: ${props => props.theme.card.background};
  border-radius: 4px;
  margin-bottom: 0.5rem;
  border-left: 3px solid ${props => props.theme.primary};

  .activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
  }

  .user {
    font-weight: 500;
    color: ${props => props.theme.text};
  }

  .time {
    font-size: 0.75rem;
    color: ${props => props.theme.textSecondary};
  }

  .action {
    font-size: 0.875rem;
    color: ${props => props.theme.textSecondary};
  }
`;

const WorkspaceHeader = styled.div`
  padding: 1rem;
  border-bottom: 1px solid ${props => props.theme.divider};
  display: flex;
  justify-content: space-between;
  align-items: center;

  h2 {
    margin: 0;
    color: ${props => props.theme.text};
  }
`;

const WorkspaceContent = styled.div`
  flex: 1;
  padding: 2rem;
  overflow: auto;
`;

const ChatHeader = styled.div`
  padding: 1rem;
  border-bottom: 1px solid ${props => props.theme.divider};

  h3 {
    margin: 0;
    color: ${props => props.theme.text};
  }
`;

const ChatMessages = styled.div`
  flex: 1;
  padding: 1rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const Message = styled(motion.div)`
  display: flex;
  gap: 0.75rem;
  align-items: flex-start;

  .avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    background: ${props => props.userColor || props.theme.primary};
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 0.875rem;
    flex-shrink: 0;
  }

  .content {
    flex: 1;
  }

  .header {
    display: flex;
    gap: 0.5rem;
    align-items: center;
    margin-bottom: 0.25rem;
  }

  .username {
    font-weight: 500;
    color: ${props => props.theme.text};
  }

  .timestamp {
    font-size: 0.75rem;
    color: ${props => props.theme.textSecondary};
  }

  .text {
    color: ${props => props.theme.text};
    line-height: 1.4;
  }
`;

const ChatInput = styled.div`
  padding: 1rem;
  border-top: 1px solid ${props => props.theme.divider};
  display: flex;
  gap: 0.5rem;
`;

const Input = styled.input`
  flex: 1;
  padding: 0.5rem;
  border: 1px solid ${props => props.theme.divider};
  border-radius: 4px;
  background: ${props => props.theme.input.background};
  color: ${props => props.theme.input.text};
`;

const Button = styled.button`
  padding: 0.5rem 1rem;
  background: ${props => props.theme.primary};
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;

  &:hover {
    background: ${props => props.theme.primaryDark};
  }
`;

const PermissionBadge = styled.span`
  padding: 0.25rem 0.5rem;
  background: ${props => props.theme.tag.background};
  color: ${props => props.theme.tag.text};
  border-radius: 12px;
  font-size: 0.75rem;
`;

const CollaborationHub = ({ projectId, currentUser }) => {
  const [users, setUsers] = useState([]);
  const [activities, setActivities] = useState([]);
  const [messages, setMessages] = useState([]);
  const [newMessage, setNewMessage] = useState('');
  const messagesEndRef = useRef(null);

  useEffect(() => {
    loadCollaborationData();
    // Simulate real-time updates
    const interval = setInterval(loadCollaborationData, 5000);
    return () => clearInterval(interval);
  }, [projectId]);

  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const loadCollaborationData = () => {
    // Simulate loading collaboration data
    setUsers([
      {
        id: 1,
        name: 'John Doe',
        email: '<EMAIL>',
        avatar: 'JD',
        color: '#2196F3',
        online: true,
        role: 'owner',
        lastSeen: new Date()
      },
      {
        id: 2,
        name: 'Jane Smith',
        email: '<EMAIL>',
        avatar: 'JS',
        color: '#4CAF50',
        online: true,
        role: 'editor',
        lastSeen: new Date()
      },
      {
        id: 3,
        name: 'Bob Wilson',
        email: '<EMAIL>',
        avatar: 'BW',
        color: '#FF9800',
        online: false,
        role: 'viewer',
        lastSeen: new Date(Date.now() - 3600000)
      }
    ]);

    setActivities([
      {
        id: 1,
        user: 'Jane Smith',
        action: 'Modified Button component',
        timestamp: new Date(Date.now() - 300000),
        type: 'edit'
      },
      {
        id: 2,
        user: 'John Doe',
        action: 'Added new API endpoint',
        timestamp: new Date(Date.now() - 600000),
        type: 'create'
      },
      {
        id: 3,
        user: 'Bob Wilson',
        action: 'Commented on Form validation',
        timestamp: new Date(Date.now() - 900000),
        type: 'comment'
      }
    ]);

    setMessages([
      {
        id: 1,
        user: 'Jane Smith',
        avatar: 'JS',
        color: '#4CAF50',
        text: 'Hey everyone! I just updated the button component with the new design.',
        timestamp: new Date(Date.now() - 1800000)
      },
      {
        id: 2,
        user: 'John Doe',
        avatar: 'JD',
        color: '#2196F3',
        text: 'Looks great! Can you also update the hover states?',
        timestamp: new Date(Date.now() - 1500000)
      },
      {
        id: 3,
        user: 'Jane Smith',
        avatar: 'JS',
        color: '#4CAF50',
        text: 'Sure thing! Working on it now.',
        timestamp: new Date(Date.now() - 1200000)
      }
    ]);
  };

  const sendMessage = () => {
    if (!newMessage.trim()) return;

    const message = {
      id: Date.now(),
      user: currentUser?.name || 'You',
      avatar: currentUser?.avatar || 'Y',
      color: currentUser?.color || '#9C27B0',
      text: newMessage,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, message]);
    setNewMessage('');

    // Simulate sending to other users
    setTimeout(() => {
      const responses = [
        'Got it!',
        'Thanks for the update!',
        'Looks good to me.',
        'I agree with that approach.',
        'Let me take a look.'
      ];
      
      const randomResponse = responses[Math.floor(Math.random() * responses.length)];
      const randomUser = users[Math.floor(Math.random() * users.length)];
      
      if (randomUser && Math.random() > 0.5) {
        setMessages(prev => [...prev, {
          id: Date.now() + 1,
          user: randomUser.name,
          avatar: randomUser.avatar,
          color: randomUser.color,
          text: randomResponse,
          timestamp: new Date()
        }]);
      }
    }, 2000);
  };

  const formatTime = (date) => {
    return new Intl.RelativeTimeFormat('en', { numeric: 'auto' }).format(
      Math.ceil((date - new Date()) / (1000 * 60)),
      'minute'
    );
  };

  return (
    <HubContainer>
      <Sidebar>
        <UserList>
          <h3>Team Members ({users.filter(u => u.online).length} online)</h3>
          <AnimatePresence>
            {users.map(user => (
              <UserItem
                key={user.id}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
              >
                <UserAvatar color={user.color}>
                  {user.avatar}
                </UserAvatar>
                <UserInfo>
                  <div className="name">{user.name}</div>
                  <div className="status">
                    {user.online ? 'Online' : `Last seen ${formatTime(user.lastSeen)}`}
                  </div>
                </UserInfo>
                <div style={{ display: 'flex', flexDirection: 'column', alignItems: 'flex-end', gap: '0.25rem' }}>
                  <StatusIndicator online={user.online} />
                  <PermissionBadge>{user.role}</PermissionBadge>
                </div>
              </UserItem>
            ))}
          </AnimatePresence>
        </UserList>

        <ProjectActivity>
          <h3>Recent Activity</h3>
          <AnimatePresence>
            {activities.map(activity => (
              <ActivityItem
                key={activity.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
              >
                <div className="activity-header">
                  <span className="user">{activity.user}</span>
                  <span className="time">{formatTime(activity.timestamp)}</span>
                </div>
                <div className="action">{activity.action}</div>
              </ActivityItem>
            ))}
          </AnimatePresence>
        </ProjectActivity>
      </Sidebar>

      <MainArea>
        <WorkspaceHeader>
          <h2>Collaborative Workspace</h2>
          <div style={{ display: 'flex', gap: '0.5rem' }}>
            <Button>Share Project</Button>
            <Button>Manage Permissions</Button>
          </div>
        </WorkspaceHeader>
        <WorkspaceContent>
          <div style={{ textAlign: 'center', padding: '4rem', color: '#666' }}>
            <h3>Real-time Collaboration</h3>
            <p>This workspace supports real-time collaboration with live cursors, shared editing, and instant updates.</p>
            <p>Select a component or file to start collaborating with your team.</p>
          </div>
        </WorkspaceContent>
      </MainArea>

      <ChatPanel>
        <ChatHeader>
          <h3>Team Chat</h3>
        </ChatHeader>
        <ChatMessages>
          <AnimatePresence>
            {messages.map(message => (
              <Message
                key={message.id}
                userColor={message.color}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
              >
                <div className="avatar">{message.avatar}</div>
                <div className="content">
                  <div className="header">
                    <span className="username">{message.user}</span>
                    <span className="timestamp">
                      {message.timestamp.toLocaleTimeString()}
                    </span>
                  </div>
                  <div className="text">{message.text}</div>
                </div>
              </Message>
            ))}
          </AnimatePresence>
          <div ref={messagesEndRef} />
        </ChatMessages>
        <ChatInput>
          <Input
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            placeholder="Type a message..."
            onKeyPress={(e) => e.key === 'Enter' && sendMessage()}
          />
          <Button onClick={sendMessage}>Send</Button>
        </ChatInput>
      </ChatPanel>
    </HubContainer>
  );
};

export default CollaborationHub;
