{"name": "client", "version": "0.1.0", "private": true, "dependencies": {"@emotion/react": "^11.11.3", "@emotion/styled": "^11.11.0", "@mui/icons-material": "^5.15.10", "@mui/material": "^5.15.10", "@testing-library/jest-dom": "^5.17.0", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@monaco-editor/react": "^4.6.0", "axios": "^1.6.7", "chart.js": "^4.4.0", "cypress": "^13.6.0", "framer-motion": "^10.16.0", "jest": "^29.7.0", "monaco-editor": "^0.45.0", "playwright": "^1.40.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^18.2.0", "react-frame-component": "^5.2.6", "react-resizable": "^3.0.5", "react-router-dom": "^6.22.1", "react-scripts": "5.0.1", "socket.io-client": "^4.7.4", "styled-components": "^6.1.8", "web-vitals": "^2.1.4", "yjs": "^13.6.10", "y-websocket": "^1.5.0"}, "devDependencies": {"@babel/plugin-proposal-private-property-in-object": "^7.21.11"}, "scripts": {"start": "set PORT=3000 && react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "test:coverage": "react-scripts test --coverage --watchAll=false", "test:e2e": "cypress run", "test:e2e:open": "cypress open", "test:playwright": "playwright test", "test:all": "npm run test:coverage && npm run test:e2e && npm run test:playwright", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}