import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';

const AssistantContainer = styled(motion.div)`
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 400px;
  max-height: 600px;
  background: ${props => props.theme.surface};
  border-radius: 12px;
  box-shadow: ${props => props.theme.shadows.large};
  z-index: 1000;
  overflow: hidden;
`;

const AssistantHeader = styled.div`
  padding: 1rem;
  background: ${props => props.theme.primary};
  color: white;
  display: flex;
  justify-content: space-between;
  align-items: center;

  h3 {
    margin: 0;
    font-size: 1rem;
  }
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 0;
`;

const ChatContainer = styled.div`
  height: 400px;
  overflow-y: auto;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const Message = styled(motion.div)`
  padding: 0.75rem;
  border-radius: 8px;
  max-width: 80%;
  align-self: ${props => props.isUser ? 'flex-end' : 'flex-start'};
  background: ${props => props.isUser ? props.theme.primary : props.theme.card.background};
  color: ${props => props.isUser ? 'white' : props.theme.text};
`;

const InputContainer = styled.div`
  padding: 1rem;
  border-top: 1px solid ${props => props.theme.divider};
  display: flex;
  gap: 0.5rem;
`;

const Input = styled.input`
  flex: 1;
  padding: 0.5rem;
  border: 1px solid ${props => props.theme.divider};
  border-radius: 4px;
  background: ${props => props.theme.input.background};
  color: ${props => props.theme.input.text};
`;

const SendButton = styled.button`
  padding: 0.5rem 1rem;
  background: ${props => props.theme.primary};
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const SuggestionList = styled.div`
  padding: 1rem;
  border-top: 1px solid ${props => props.theme.divider};
`;

const SuggestionItem = styled(motion.div)`
  padding: 0.5rem;
  background: ${props => props.theme.card.background};
  border: 1px solid ${props => props.theme.divider};
  border-radius: 4px;
  margin-bottom: 0.5rem;
  cursor: pointer;
  transition: background 0.2s;

  &:hover {
    background: ${props => props.theme.hover};
  }
`;

const FloatingButton = styled(motion.button)`
  position: fixed;
  bottom: 20px;
  right: 20px;
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: ${props => props.theme.primary};
  color: white;
  border: none;
  cursor: pointer;
  box-shadow: ${props => props.theme.shadows.large};
  font-size: 1.5rem;
  z-index: 999;
`;

const AIAssistant = ({ onCodeSuggestion, currentCode, language = 'javascript' }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState([]);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [suggestions, setSuggestions] = useState([]);
  const chatEndRef = useRef(null);

  useEffect(() => {
    if (isOpen) {
      // Initialize with welcome message
      setMessages([
        {
          id: 1,
          text: "Hi! I'm your AI coding assistant. I can help you with code suggestions, bug fixes, refactoring, and explanations. What would you like to work on?",
          isUser: false,
          timestamp: new Date()
        }
      ]);
      
      // Generate initial suggestions based on current code
      generateSuggestions();
    }
  }, [isOpen, currentCode]);

  useEffect(() => {
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const generateSuggestions = () => {
    // Simulate AI-generated suggestions based on current code
    const mockSuggestions = [
      {
        id: 1,
        title: "Add error handling",
        description: "Consider adding try-catch blocks for better error handling",
        code: `try {
  // Your code here
} catch (error) {
  console.error('Error:', error);
}`
      },
      {
        id: 2,
        title: "Optimize performance",
        description: "Use React.memo to prevent unnecessary re-renders",
        code: `const OptimizedComponent = React.memo(({ prop1, prop2 }) => {
  // Component logic
});`
      },
      {
        id: 3,
        title: "Add TypeScript types",
        description: "Define proper TypeScript interfaces for better type safety",
        code: `interface Props {
  title: string;
  onClick: () => void;
  disabled?: boolean;
}`
      }
    ];

    setSuggestions(mockSuggestions);
  };

  const handleSendMessage = async () => {
    if (!input.trim() || isLoading) return;

    const userMessage = {
      id: Date.now(),
      text: input,
      isUser: true,
      timestamp: new Date()
    };

    setMessages(prev => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    // Simulate AI response
    setTimeout(() => {
      const aiResponse = generateAIResponse(input);
      setMessages(prev => [...prev, {
        id: Date.now() + 1,
        text: aiResponse.text,
        isUser: false,
        timestamp: new Date(),
        code: aiResponse.code
      }]);
      setIsLoading(false);
    }, 1000);
  };

  const generateAIResponse = (userInput) => {
    const input_lower = userInput.toLowerCase();
    
    if (input_lower.includes('bug') || input_lower.includes('error')) {
      return {
        text: "I can help you debug your code. Here's a common pattern for error handling:",
        code: `// Add proper error boundaries
class ErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true };
  }

  componentDidCatch(error, errorInfo) {
    console.log('Error caught:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return <h1>Something went wrong.</h1>;
    }
    return this.props.children;
  }
}`
      };
    }
    
    if (input_lower.includes('optimize') || input_lower.includes('performance')) {
      return {
        text: "Here are some performance optimization techniques:",
        code: `// Use React.memo for component optimization
const OptimizedComponent = React.memo(({ data }) => {
  return <div>{data.map(item => <Item key={item.id} {...item} />)}</div>;
});

// Use useMemo for expensive calculations
const expensiveValue = useMemo(() => {
  return heavyCalculation(data);
}, [data]);

// Use useCallback for event handlers
const handleClick = useCallback(() => {
  // Handle click
}, [dependency]);`
      };
    }

    return {
      text: "I understand you're looking for help with your code. Could you be more specific about what you'd like assistance with? I can help with debugging, optimization, refactoring, or explaining concepts.",
      code: null
    };
  };

  const applySuggestion = (suggestion) => {
    if (onCodeSuggestion) {
      onCodeSuggestion(suggestion.code);
    }
    
    setMessages(prev => [...prev, {
      id: Date.now(),
      text: `Applied suggestion: ${suggestion.title}`,
      isUser: false,
      timestamp: new Date(),
      code: suggestion.code
    }]);
  };

  if (!isOpen) {
    return (
      <FloatingButton
        onClick={() => setIsOpen(true)}
        whileHover={{ scale: 1.1 }}
        whileTap={{ scale: 0.9 }}
      >
        🤖
      </FloatingButton>
    );
  }

  return (
    <AssistantContainer
      initial={{ opacity: 0, y: 100 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: 100 }}
    >
      <AssistantHeader>
        <h3>AI Coding Assistant</h3>
        <CloseButton onClick={() => setIsOpen(false)}>×</CloseButton>
      </AssistantHeader>

      <ChatContainer>
        <AnimatePresence>
          {messages.map(message => (
            <Message
              key={message.id}
              isUser={message.isUser}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
            >
              <div>{message.text}</div>
              {message.code && (
                <pre style={{ 
                  background: 'rgba(0,0,0,0.1)', 
                  padding: '0.5rem', 
                  borderRadius: '4px',
                  marginTop: '0.5rem',
                  fontSize: '0.875rem',
                  overflow: 'auto'
                }}>
                  <code>{message.code}</code>
                </pre>
              )}
            </Message>
          ))}
        </AnimatePresence>
        {isLoading && (
          <Message isUser={false}>
            <div>Thinking...</div>
          </Message>
        )}
        <div ref={chatEndRef} />
      </ChatContainer>

      <SuggestionList>
        <h4 style={{ margin: '0 0 0.5rem 0', fontSize: '0.875rem' }}>Quick Suggestions:</h4>
        {suggestions.map(suggestion => (
          <SuggestionItem
            key={suggestion.id}
            onClick={() => applySuggestion(suggestion)}
            whileHover={{ scale: 1.02 }}
            whileTap={{ scale: 0.98 }}
          >
            <strong>{suggestion.title}</strong>
            <div style={{ fontSize: '0.875rem', opacity: 0.8 }}>
              {suggestion.description}
            </div>
          </SuggestionItem>
        ))}
      </SuggestionList>

      <InputContainer>
        <Input
          value={input}
          onChange={(e) => setInput(e.target.value)}
          placeholder="Ask me anything about your code..."
          onKeyPress={(e) => e.key === 'Enter' && handleSendMessage()}
        />
        <SendButton onClick={handleSendMessage} disabled={isLoading}>
          Send
        </SendButton>
      </InputContainer>
    </AssistantContainer>
  );
};

export default AIAssistant;
