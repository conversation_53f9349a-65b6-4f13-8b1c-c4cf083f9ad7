import { test, expect } from '@playwright/test';

test.describe('UI Builder', () => {
  test.beforeEach(async ({ page }) => {
    // Mock authentication
    await page.goto('/login');
    await page.fill('input[name="email"]', '<EMAIL>');
    await page.fill('input[name="password"]', 'password123');
    await page.click('button[type="submit"]');
    await page.waitForURL('/dashboard');
    
    // Navigate to UI Builder
    await page.goto('/ui-builder');
  });

  test('should load UI Builder interface', async ({ page }) => {
    await expect(page.locator('[data-testid="component-library"]')).toBeVisible();
    await expect(page.locator('[data-testid="canvas"]')).toBeVisible();
    await expect(page.locator('[data-testid="property-panel"]')).toBeVisible();
  });

  test('should drag and drop components', async ({ page }) => {
    // Drag a button component from the library
    const buttonComponent = page.locator('[data-testid="component-button"]');
    const canvas = page.locator('[data-testid="canvas"]');
    
    await buttonComponent.dragTo(canvas);
    
    // Verify component was added to canvas
    await expect(canvas.locator('.component-button')).toBeVisible();
  });

  test('should edit component properties', async ({ page }) => {
    // Add a button component
    const buttonComponent = page.locator('[data-testid="component-button"]');
    const canvas = page.locator('[data-testid="canvas"]');
    
    await buttonComponent.dragTo(canvas);
    
    // Select the component
    await canvas.locator('.component-button').click();
    
    // Edit properties
    await page.fill('[data-testid="property-text"]', 'Custom Button Text');
    await page.selectOption('[data-testid="property-variant"]', 'primary');
    
    // Verify changes
    await expect(canvas.locator('.component-button')).toContainText('Custom Button Text');
    await expect(canvas.locator('.component-button')).toHaveClass(/primary/);
  });

  test('should switch between responsive viewports', async ({ page }) => {
    // Test desktop view
    await page.click('[data-testid="viewport-desktop"]');
    await expect(page.locator('[data-testid="canvas"]')).toHaveCSS('width', '1200px');
    
    // Test tablet view
    await page.click('[data-testid="viewport-tablet"]');
    await expect(page.locator('[data-testid="canvas"]')).toHaveCSS('width', '768px');
    
    // Test mobile view
    await page.click('[data-testid="viewport-mobile"]');
    await expect(page.locator('[data-testid="canvas"]')).toHaveCSS('width', '375px');
  });

  test('should generate code preview', async ({ page }) => {
    // Add components
    const buttonComponent = page.locator('[data-testid="component-button"]');
    const canvas = page.locator('[data-testid="canvas"]');
    
    await buttonComponent.dragTo(canvas);
    
    // Open code preview
    await page.click('[data-testid="code-preview-button"]');
    
    // Verify code is generated
    const codePreview = page.locator('[data-testid="code-preview"]');
    await expect(codePreview).toBeVisible();
    await expect(codePreview).toContainText('<Button');
  });

  test('should save and load projects', async ({ page }) => {
    // Add some components
    const buttonComponent = page.locator('[data-testid="component-button"]');
    const canvas = page.locator('[data-testid="canvas"]');
    
    await buttonComponent.dragTo(canvas);
    
    // Save project
    await page.click('[data-testid="save-project"]');
    await page.fill('[data-testid="project-name"]', 'Test Project');
    await page.click('[data-testid="confirm-save"]');
    
    // Verify save success
    await expect(page.locator('[data-testid="save-success"]')).toBeVisible();
    
    // Clear canvas
    await page.click('[data-testid="clear-canvas"]');
    await expect(canvas.locator('.component-button')).not.toBeVisible();
    
    // Load project
    await page.click('[data-testid="load-project"]');
    await page.click('[data-testid="project-Test Project"]');
    
    // Verify components are restored
    await expect(canvas.locator('.component-button')).toBeVisible();
  });

  test('should handle undo/redo operations', async ({ page }) => {
    const buttonComponent = page.locator('[data-testid="component-button"]');
    const canvas = page.locator('[data-testid="canvas"]');
    
    // Add component
    await buttonComponent.dragTo(canvas);
    await expect(canvas.locator('.component-button')).toBeVisible();
    
    // Undo
    await page.keyboard.press('Control+Z');
    await expect(canvas.locator('.component-button')).not.toBeVisible();
    
    // Redo
    await page.keyboard.press('Control+Y');
    await expect(canvas.locator('.component-button')).toBeVisible();
  });
});
