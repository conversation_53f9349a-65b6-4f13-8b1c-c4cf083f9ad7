{"name": "fullstack-dev-pro-server", "version": "1.0.0", "description": "Backend server for FullStack Dev Pro", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"bcryptjs": "^2.4.3", "config": "^3.3.9", "cors": "^2.8.5", "express": "^4.18.2", "express-validator": "^7.0.1", "helmet": "^7.1.0", "jsonwebtoken": "^9.0.2", "mongoose": "^7.6.5", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "nodemailer": "^6.9.7", "socket.io": "^4.7.4", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.0", "ws": "^8.14.2", "yjs": "^13.6.10", "y-websocket": "^1.5.0"}, "devDependencies": {"jest": "^29.7.0", "nodemon": "^3.0.1"}}