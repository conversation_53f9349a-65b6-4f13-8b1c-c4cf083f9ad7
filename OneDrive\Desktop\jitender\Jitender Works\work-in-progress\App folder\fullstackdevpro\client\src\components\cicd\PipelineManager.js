import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';

const ManagerContainer = styled.div`
  padding: 2rem;
  background: ${props => props.theme.background};
  height: 100vh;
  overflow: auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;

  h1 {
    margin: 0;
    color: ${props => props.theme.text};
  }
`;

const PipelineGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 2rem;
  height: calc(100vh - 150px);
`;

const Panel = styled.div`
  background: ${props => props.theme.surface};
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: ${props => props.theme.shadows.medium};
  overflow: auto;

  h3 {
    margin-top: 0;
    color: ${props => props.theme.text};
  }
`;

const PipelineList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const PipelineItem = styled(motion.div)`
  padding: 1rem;
  background: ${props => props.theme.card.background};
  border: 1px solid ${props => props.theme.divider};
  border-left: 4px solid ${props => getStatusColor(props.status)};
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    transform: translateX(4px);
    box-shadow: ${props => props.theme.shadows.small};
  }

  .pipeline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
  }

  .pipeline-name {
    font-weight: 500;
    color: ${props => props.theme.text};
  }

  .pipeline-status {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    background: ${props => getStatusColor(props.status)};
    color: white;
  }

  .pipeline-info {
    font-size: 0.875rem;
    color: ${props => props.theme.textSecondary};
  }
`;

const StageList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const StageItem = styled(motion.div)`
  padding: 0.75rem;
  background: ${props => props.theme.card.background};
  border: 1px solid ${props => props.theme.divider};
  border-radius: 4px;
  display: flex;
  align-items: center;
  gap: 1rem;

  .stage-icon {
    width: 24px;
    height: 24px;
    border-radius: 50%;
    background: ${props => getStatusColor(props.status)};
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 0.75rem;
  }

  .stage-info {
    flex: 1;
  }

  .stage-name {
    font-weight: 500;
    color: ${props => props.theme.text};
  }

  .stage-duration {
    font-size: 0.875rem;
    color: ${props => props.theme.textSecondary};
  }

  .stage-status {
    font-size: 0.875rem;
    color: ${props => getStatusColor(props.status)};
    font-weight: 500;
  }
`;

const LogViewer = styled.pre`
  background: ${props => props.theme.code.background};
  color: ${props => props.theme.code.text};
  padding: 1rem;
  border-radius: 4px;
  font-family: monospace;
  font-size: 0.875rem;
  line-height: 1.5;
  overflow: auto;
  max-height: 400px;
  margin-top: 1rem;
`;

const ActionBar = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
`;

const Button = styled.button`
  padding: 0.5rem 1rem;
  background: ${props => props.primary ? props.theme.primary : props.theme.surface};
  color: ${props => props.primary ? 'white' : props.theme.text};
  border: 1px solid ${props => props.primary ? 'transparent' : props.theme.divider};
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: ${props => props.primary ? props.theme.primaryDark : props.theme.hover};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const ConfigForm = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-top: 1rem;

  label {
    font-weight: 500;
    color: ${props => props.theme.text};
  }

  input, select, textarea {
    padding: 0.5rem;
    border: 1px solid ${props => props.theme.divider};
    border-radius: 4px;
    background: ${props => props.theme.input.background};
    color: ${props => props.theme.input.text};
  }

  textarea {
    min-height: 100px;
    resize: vertical;
  }
`;

const getStatusColor = (status) => {
  const colors = {
    success: '#4CAF50',
    failed: '#F44336',
    running: '#2196F3',
    pending: '#FFC107',
    cancelled: '#757575'
  };
  return colors[status] || '#757575';
};

const getStatusIcon = (status) => {
  const icons = {
    success: '✓',
    failed: '✗',
    running: '⟳',
    pending: '⏸',
    cancelled: '⏹'
  };
  return icons[status] || '?';
};

const PipelineManager = ({ projectId }) => {
  const [pipelines, setPipelines] = useState([]);
  const [selectedPipeline, setSelectedPipeline] = useState(null);
  const [stages, setStages] = useState([]);
  const [logs, setLogs] = useState('');
  const [showConfig, setShowConfig] = useState(false);

  useEffect(() => {
    loadPipelines();
  }, [projectId]);

  useEffect(() => {
    if (selectedPipeline) {
      loadPipelineStages(selectedPipeline.id);
    }
  }, [selectedPipeline]);

  const loadPipelines = () => {
    // Simulate loading pipelines
    const mockPipelines = [
      {
        id: 1,
        name: 'Main Branch CI/CD',
        status: 'success',
        lastRun: new Date(Date.now() - 3600000),
        duration: '5m 23s',
        trigger: 'push',
        branch: 'main'
      },
      {
        id: 2,
        name: 'Feature Branch Testing',
        status: 'running',
        lastRun: new Date(),
        duration: '2m 45s',
        trigger: 'pull_request',
        branch: 'feature/ui-improvements'
      },
      {
        id: 3,
        name: 'Nightly Build',
        status: 'failed',
        lastRun: new Date(Date.now() - 86400000),
        duration: '8m 12s',
        trigger: 'schedule',
        branch: 'main'
      }
    ];

    setPipelines(mockPipelines);
    if (!selectedPipeline && mockPipelines.length > 0) {
      setSelectedPipeline(mockPipelines[0]);
    }
  };

  const loadPipelineStages = (pipelineId) => {
    // Simulate loading pipeline stages
    const mockStages = [
      {
        id: 1,
        name: 'Checkout Code',
        status: 'success',
        duration: '15s',
        startTime: new Date(Date.now() - 300000)
      },
      {
        id: 2,
        name: 'Install Dependencies',
        status: 'success',
        duration: '1m 23s',
        startTime: new Date(Date.now() - 285000)
      },
      {
        id: 3,
        name: 'Run Tests',
        status: 'success',
        duration: '2m 45s',
        startTime: new Date(Date.now() - 225000)
      },
      {
        id: 4,
        name: 'Build Application',
        status: 'running',
        duration: '1m 12s',
        startTime: new Date(Date.now() - 72000)
      },
      {
        id: 5,
        name: 'Deploy to Staging',
        status: 'pending',
        duration: null,
        startTime: null
      }
    ];

    setStages(mockStages);
    
    // Simulate logs
    setLogs(`
[2024-01-15 10:30:15] Starting pipeline execution...
[2024-01-15 10:30:16] Checking out code from main branch
[2024-01-15 10:30:31] Code checkout completed successfully
[2024-01-15 10:30:32] Installing dependencies...
[2024-01-15 10:31:55] Dependencies installed successfully
[2024-01-15 10:31:56] Running test suite...
[2024-01-15 10:34:41] All tests passed (45 tests, 0 failures)
[2024-01-15 10:34:42] Building application...
[2024-01-15 10:35:54] Build completed successfully
[2024-01-15 10:35:55] Deploying to staging environment...
    `);
  };

  const handleRunPipeline = (pipeline) => {
    console.log('Running pipeline:', pipeline.name);
    // Update pipeline status to running
    setPipelines(prev => 
      prev.map(p => 
        p.id === pipeline.id 
          ? { ...p, status: 'running', lastRun: new Date() }
          : p
      )
    );
  };

  const handleStopPipeline = (pipeline) => {
    console.log('Stopping pipeline:', pipeline.name);
    setPipelines(prev => 
      prev.map(p => 
        p.id === pipeline.id 
          ? { ...p, status: 'cancelled' }
          : p
      )
    );
  };

  const formatDate = (date) => {
    return new Intl.RelativeTimeFormat('en', { numeric: 'auto' }).format(
      Math.ceil((date - new Date()) / (1000 * 60)),
      'minute'
    );
  };

  return (
    <ManagerContainer>
      <Header>
        <h1>CI/CD Pipeline Manager</h1>
        <ActionBar>
          <Button primary onClick={() => handleRunPipeline(selectedPipeline)} disabled={!selectedPipeline}>
            Run Pipeline
          </Button>
          <Button onClick={() => handleStopPipeline(selectedPipeline)} disabled={!selectedPipeline || selectedPipeline.status !== 'running'}>
            Stop Pipeline
          </Button>
          <Button onClick={() => setShowConfig(!showConfig)}>
            Configure
          </Button>
        </ActionBar>
      </Header>

      <PipelineGrid>
        <Panel>
          <h3>Pipelines</h3>
          <PipelineList>
            <AnimatePresence>
              {pipelines.map(pipeline => (
                <PipelineItem
                  key={pipeline.id}
                  status={pipeline.status}
                  onClick={() => setSelectedPipeline(pipeline)}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                >
                  <div className="pipeline-header">
                    <span className="pipeline-name">{pipeline.name}</span>
                    <span className="pipeline-status">{pipeline.status}</span>
                  </div>
                  <div className="pipeline-info">
                    Branch: {pipeline.branch} • {formatDate(pipeline.lastRun)} • {pipeline.duration}
                  </div>
                </PipelineItem>
              ))}
            </AnimatePresence>
          </PipelineList>

          {showConfig && (
            <ConfigForm>
              <h4>Pipeline Configuration</h4>
              <label>
                Pipeline Name:
                <input type="text" defaultValue={selectedPipeline?.name} />
              </label>
              <label>
                Trigger:
                <select defaultValue={selectedPipeline?.trigger}>
                  <option value="push">On Push</option>
                  <option value="pull_request">On Pull Request</option>
                  <option value="schedule">Scheduled</option>
                  <option value="manual">Manual</option>
                </select>
              </label>
              <label>
                Branch:
                <input type="text" defaultValue={selectedPipeline?.branch} />
              </label>
              <label>
                Pipeline Script:
                <textarea placeholder="Enter your pipeline configuration (YAML)..." />
              </label>
              <Button primary>Save Configuration</Button>
            </ConfigForm>
          )}
        </Panel>

        <Panel>
          <h3>Pipeline Stages</h3>
          {selectedPipeline && (
            <>
              <div style={{ marginBottom: '1rem', padding: '0.5rem', background: '#f5f5f5', borderRadius: '4px' }}>
                <strong>{selectedPipeline.name}</strong> - {selectedPipeline.status}
              </div>
              
              <StageList>
                <AnimatePresence>
                  {stages.map(stage => (
                    <StageItem
                      key={stage.id}
                      status={stage.status}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      exit={{ opacity: 0, x: 20 }}
                    >
                      <div className="stage-icon">
                        {getStatusIcon(stage.status)}
                      </div>
                      <div className="stage-info">
                        <div className="stage-name">{stage.name}</div>
                        <div className="stage-duration">
                          {stage.duration || 'Not started'}
                        </div>
                      </div>
                      <div className="stage-status">{stage.status}</div>
                    </StageItem>
                  ))}
                </AnimatePresence>
              </StageList>

              <LogViewer>{logs}</LogViewer>
            </>
          )}
        </Panel>
      </PipelineGrid>
    </ManagerContainer>
  );
};

export default PipelineManager;
