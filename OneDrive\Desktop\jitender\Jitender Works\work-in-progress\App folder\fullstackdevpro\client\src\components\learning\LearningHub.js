import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';

const HubContainer = styled.div`
  padding: 2rem;
  background: ${props => props.theme.background};
  min-height: 100vh;
`;

const Header = styled.div`
  text-align: center;
  margin-bottom: 3rem;

  h1 {
    color: ${props => props.theme.text};
    margin-bottom: 1rem;
  }

  p {
    color: ${props => props.theme.textSecondary};
    font-size: 1.1rem;
  }
`;

const TabContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-bottom: 3rem;
  border-bottom: 1px solid ${props => props.theme.divider};
`;

const Tab = styled.button`
  padding: 1rem 2rem;
  background: ${props => props.active ? props.theme.primary : 'transparent'};
  color: ${props => props.active ? 'white' : props.theme.text};
  border: none;
  border-bottom: 2px solid ${props => props.active ? props.theme.primary : 'transparent'};
  cursor: pointer;
  transition: all 0.2s;
  font-size: 1rem;

  &:hover {
    background: ${props => props.active ? props.theme.primaryDark : props.theme.hover};
  }
`;

const ContentGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 2rem;
  max-width: 1200px;
  margin: 0 auto;
`;

const ContentCard = styled(motion.div)`
  background: ${props => props.theme.surface};
  border-radius: 12px;
  overflow: hidden;
  box-shadow: ${props => props.theme.shadows.medium};
  transition: transform 0.2s;

  &:hover {
    transform: translateY(-4px);
  }
`;

const CardImage = styled.div`
  height: 200px;
  background: ${props => props.image ? `url(${props.image})` : `linear-gradient(135deg, ${props.theme.primary}, ${props.theme.primaryDark})`};
  background-size: cover;
  background-position: center;
  position: relative;

  .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: opacity 0.2s;
  }

  &:hover .overlay {
    opacity: 1;
  }

  .play-button {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: ${props => props.theme.primary};
  }
`;

const CardContent = styled.div`
  padding: 1.5rem;
`;

const CardHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: start;
  margin-bottom: 1rem;

  h3 {
    margin: 0;
    color: ${props => props.theme.text};
    font-size: 1.2rem;
  }
`;

const DifficultyBadge = styled.span`
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
  background: ${props => getDifficultyColor(props.level)};
  color: white;
`;

const CardDescription = styled.p`
  color: ${props => props.theme.textSecondary};
  margin-bottom: 1rem;
  line-height: 1.5;
`;

const CardMeta = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 0.875rem;
  color: ${props => props.theme.textSecondary};
`;

const TagList = styled.div`
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  margin-bottom: 1rem;
`;

const Tag = styled.span`
  padding: 0.25rem 0.5rem;
  background: ${props => props.theme.tag.background};
  color: ${props => props.theme.tag.text};
  border-radius: 4px;
  font-size: 0.75rem;
`;

const ProgressBar = styled.div`
  width: 100%;
  height: 4px;
  background: ${props => props.theme.divider};
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 1rem;

  .fill {
    height: 100%;
    background: ${props => props.theme.primary};
    width: ${props => props.progress}%;
    transition: width 0.3s;
  }
`;

const ActionButton = styled.button`
  width: 100%;
  padding: 0.75rem;
  background: ${props => props.primary ? props.theme.primary : props.theme.surface};
  color: ${props => props.primary ? 'white' : props.theme.text};
  border: 1px solid ${props => props.primary ? 'transparent' : props.theme.divider};
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s;
  font-weight: 500;

  &:hover {
    background: ${props => props.primary ? props.theme.primaryDark : props.theme.hover};
  }
`;

const SearchContainer = styled.div`
  max-width: 600px;
  margin: 0 auto 3rem auto;
  display: flex;
  gap: 1rem;
`;

const SearchInput = styled.input`
  flex: 1;
  padding: 0.75rem;
  border: 1px solid ${props => props.theme.divider};
  border-radius: 8px;
  background: ${props => props.theme.input.background};
  color: ${props => props.theme.input.text};
  font-size: 1rem;
`;

const FilterSelect = styled.select`
  padding: 0.75rem;
  border: 1px solid ${props => props.theme.divider};
  border-radius: 8px;
  background: ${props => props.theme.input.background};
  color: ${props => props.theme.input.text};
`;

const getDifficultyColor = (level) => {
  const colors = {
    beginner: '#4CAF50',
    intermediate: '#FF9800',
    advanced: '#F44336'
  };
  return colors[level] || '#757575';
};

const LearningHub = () => {
  const [activeTab, setActiveTab] = useState('tutorials');
  const [content, setContent] = useState([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterLevel, setFilterLevel] = useState('all');

  useEffect(() => {
    loadContent(activeTab);
  }, [activeTab]);

  const loadContent = (type) => {
    const mockContent = {
      tutorials: [
        {
          id: 1,
          title: 'React Fundamentals',
          description: 'Learn the basics of React including components, props, and state management.',
          duration: '2 hours',
          difficulty: 'beginner',
          progress: 75,
          tags: ['React', 'JavaScript', 'Frontend'],
          image: null,
          type: 'video'
        },
        {
          id: 2,
          title: 'Building REST APIs with Node.js',
          description: 'Complete guide to creating scalable REST APIs using Node.js and Express.',
          duration: '3 hours',
          difficulty: 'intermediate',
          progress: 30,
          tags: ['Node.js', 'Express', 'Backend', 'API'],
          image: null,
          type: 'video'
        },
        {
          id: 3,
          title: 'Database Design Principles',
          description: 'Learn how to design efficient and scalable database schemas.',
          duration: '1.5 hours',
          difficulty: 'intermediate',
          progress: 0,
          tags: ['Database', 'SQL', 'Design'],
          image: null,
          type: 'article'
        }
      ],
      challenges: [
        {
          id: 4,
          title: 'Build a Todo App',
          description: 'Create a full-stack todo application with CRUD operations.',
          duration: '4 hours',
          difficulty: 'beginner',
          progress: 100,
          tags: ['React', 'Node.js', 'MongoDB'],
          image: null,
          type: 'challenge'
        },
        {
          id: 5,
          title: 'E-commerce Platform',
          description: 'Build a complete e-commerce platform with payment integration.',
          duration: '12 hours',
          difficulty: 'advanced',
          progress: 45,
          tags: ['React', 'Node.js', 'Stripe', 'MongoDB'],
          image: null,
          type: 'challenge'
        }
      ],
      certification: [
        {
          id: 6,
          title: 'Full-Stack Developer Certification',
          description: 'Comprehensive certification covering frontend, backend, and database technologies.',
          duration: '40 hours',
          difficulty: 'advanced',
          progress: 20,
          tags: ['React', 'Node.js', 'MongoDB', 'Certification'],
          image: null,
          type: 'certification'
        },
        {
          id: 7,
          title: 'React Developer Certification',
          description: 'Specialized certification focusing on React and modern frontend development.',
          duration: '25 hours',
          difficulty: 'intermediate',
          progress: 60,
          tags: ['React', 'Redux', 'Testing', 'Certification'],
          image: null,
          type: 'certification'
        }
      ]
    };

    setContent(mockContent[type] || []);
  };

  const filteredContent = content.filter(item => {
    const matchesSearch = item.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         item.tags.some(tag => tag.toLowerCase().includes(searchQuery.toLowerCase()));
    
    const matchesLevel = filterLevel === 'all' || item.difficulty === filterLevel;
    
    return matchesSearch && matchesLevel;
  });

  const handleStartContent = (item) => {
    console.log('Starting content:', item.title);
    // Here you would navigate to the content or open a modal
  };

  return (
    <HubContainer>
      <Header>
        <h1>Learning Hub</h1>
        <p>Master full-stack development with our comprehensive learning resources</p>
      </Header>

      <TabContainer>
        <Tab active={activeTab === 'tutorials'} onClick={() => setActiveTab('tutorials')}>
          Tutorials & Guides
        </Tab>
        <Tab active={activeTab === 'challenges'} onClick={() => setActiveTab('challenges')}>
          Coding Challenges
        </Tab>
        <Tab active={activeTab === 'certification'} onClick={() => setActiveTab('certification')}>
          Certification Programs
        </Tab>
      </TabContainer>

      <SearchContainer>
        <SearchInput
          type="text"
          placeholder="Search tutorials, challenges, or topics..."
          value={searchQuery}
          onChange={(e) => setSearchQuery(e.target.value)}
        />
        <FilterSelect value={filterLevel} onChange={(e) => setFilterLevel(e.target.value)}>
          <option value="all">All Levels</option>
          <option value="beginner">Beginner</option>
          <option value="intermediate">Intermediate</option>
          <option value="advanced">Advanced</option>
        </FilterSelect>
      </SearchContainer>

      <ContentGrid>
        <AnimatePresence>
          {filteredContent.map(item => (
            <ContentCard
              key={item.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: 20 }}
            >
              <CardImage image={item.image}>
                <div className="overlay">
                  <div className="play-button">
                    {item.type === 'video' ? '▶' : item.type === 'challenge' ? '🏆' : '📜'}
                  </div>
                </div>
              </CardImage>
              
              <CardContent>
                <CardHeader>
                  <h3>{item.title}</h3>
                  <DifficultyBadge level={item.difficulty}>
                    {item.difficulty}
                  </DifficultyBadge>
                </CardHeader>
                
                <CardDescription>{item.description}</CardDescription>
                
                <CardMeta>
                  <span>Duration: {item.duration}</span>
                  <span>{item.type}</span>
                </CardMeta>
                
                <TagList>
                  {item.tags.map(tag => (
                    <Tag key={tag}>{tag}</Tag>
                  ))}
                </TagList>
                
                {item.progress > 0 && (
                  <ProgressBar progress={item.progress}>
                    <div className="fill" />
                  </ProgressBar>
                )}
                
                <ActionButton 
                  primary={item.progress === 0}
                  onClick={() => handleStartContent(item)}
                >
                  {item.progress === 0 ? 'Start Learning' : 
                   item.progress === 100 ? 'Review' : 'Continue'}
                </ActionButton>
              </CardContent>
            </ContentCard>
          ))}
        </AnimatePresence>
      </ContentGrid>
    </HubContainer>
  );
};

export default LearningHub;
