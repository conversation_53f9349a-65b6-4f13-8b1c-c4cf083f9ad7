import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';

const GitContainer = styled.div`
  padding: 2rem;
  background: ${props => props.theme.background};
  height: 100vh;
  overflow: auto;
`;

const Header = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;

  h1 {
    margin: 0;
    color: ${props => props.theme.text};
  }
`;

const GitGrid = styled.div`
  display: grid;
  grid-template-columns: 300px 1fr 300px;
  gap: 2rem;
  height: calc(100vh - 150px);
`;

const Panel = styled.div`
  background: ${props => props.theme.surface};
  border-radius: 8px;
  padding: 1.5rem;
  box-shadow: ${props => props.theme.shadows.medium};
  overflow: auto;

  h3 {
    margin-top: 0;
    color: ${props => props.theme.text};
  }
`;

const BranchList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
`;

const BranchItem = styled(motion.div)`
  padding: 0.75rem;
  background: ${props => props.active ? props.theme.primary + '20' : props.theme.card.background};
  border: 1px solid ${props => props.active ? props.theme.primary : props.theme.divider};
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: ${props => props.active ? props.theme.primary + '30' : props.theme.hover};
  }

  .branch-name {
    font-weight: 500;
    color: ${props => props.theme.text};
    display: flex;
    align-items: center;
    gap: 0.5rem;
  }

  .branch-info {
    font-size: 0.875rem;
    color: ${props => props.theme.textSecondary};
    margin-top: 0.25rem;
  }
`;

const CommitList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 1rem;
`;

const CommitItem = styled(motion.div)`
  padding: 1rem;
  background: ${props => props.theme.card.background};
  border: 1px solid ${props => props.theme.divider};
  border-left: 4px solid ${props => props.theme.primary};
  border-radius: 4px;

  .commit-header {
    display: flex;
    justify-content: space-between;
    align-items: start;
    margin-bottom: 0.5rem;
  }

  .commit-message {
    font-weight: 500;
    color: ${props => props.theme.text};
    margin-bottom: 0.25rem;
  }

  .commit-hash {
    font-family: monospace;
    font-size: 0.875rem;
    color: ${props => props.theme.textSecondary};
    background: ${props => props.theme.code.background};
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
  }

  .commit-meta {
    font-size: 0.875rem;
    color: ${props => props.theme.textSecondary};
  }

  .commit-changes {
    margin-top: 0.5rem;
    font-size: 0.875rem;
  }

  .additions {
    color: #4CAF50;
  }

  .deletions {
    color: #F44336;
  }
`;

const FileChanges = styled.div`
  .file-item {
    padding: 0.5rem;
    background: ${props => props.theme.card.background};
    border: 1px solid ${props => props.theme.divider};
    border-radius: 4px;
    margin-bottom: 0.5rem;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .file-name {
    font-family: monospace;
    color: ${props => props.theme.text};
  }

  .file-status {
    padding: 0.25rem 0.5rem;
    border-radius: 12px;
    font-size: 0.75rem;
    color: white;
  }

  .modified {
    background: #FF9800;
  }

  .added {
    background: #4CAF50;
  }

  .deleted {
    background: #F44336;
  }
`;

const ActionBar = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
`;

const Button = styled.button`
  padding: 0.5rem 1rem;
  background: ${props => props.primary ? props.theme.primary : props.theme.surface};
  color: ${props => props.primary ? 'white' : props.theme.text};
  border: 1px solid ${props => props.primary ? 'transparent' : props.theme.divider};
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s;

  &:hover {
    background: ${props => props.primary ? props.theme.primaryDark : props.theme.hover};
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const CommitForm = styled.div`
  margin-top: 1rem;
  padding: 1rem;
  background: ${props => props.theme.card.background};
  border-radius: 4px;

  textarea {
    width: 100%;
    min-height: 80px;
    padding: 0.5rem;
    border: 1px solid ${props => props.theme.divider};
    border-radius: 4px;
    background: ${props => props.theme.input.background};
    color: ${props => props.theme.input.text};
    resize: vertical;
    margin-bottom: 1rem;
  }
`;

const StatusIndicator = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-bottom: 1rem;

  .status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: ${props => props.clean ? '#4CAF50' : '#FF9800'};
  }

  .status-text {
    color: ${props => props.theme.text};
  }
`;

const GitIntegration = ({ projectId }) => {
  const [branches, setBranches] = useState([]);
  const [currentBranch, setCurrentBranch] = useState('main');
  const [commits, setCommits] = useState([]);
  const [changedFiles, setChangedFiles] = useState([]);
  const [commitMessage, setCommitMessage] = useState('');
  const [isClean, setIsClean] = useState(true);

  useEffect(() => {
    loadGitData();
  }, [projectId, currentBranch]);

  const loadGitData = () => {
    // Simulate loading Git data
    setBranches([
      {
        name: 'main',
        current: true,
        lastCommit: '2 hours ago',
        ahead: 0,
        behind: 0
      },
      {
        name: 'feature/ui-improvements',
        current: false,
        lastCommit: '1 day ago',
        ahead: 3,
        behind: 1
      },
      {
        name: 'bugfix/form-validation',
        current: false,
        lastCommit: '3 days ago',
        ahead: 1,
        behind: 5
      }
    ]);

    setCommits([
      {
        id: 'a1b2c3d',
        message: 'Add new component library integration',
        author: 'John Doe',
        date: new Date(Date.now() - 7200000),
        hash: 'a1b2c3d4e5f',
        additions: 45,
        deletions: 12
      },
      {
        id: 'b2c3d4e',
        message: 'Fix responsive design issues',
        author: 'Jane Smith',
        date: new Date(Date.now() - 86400000),
        hash: 'b2c3d4e5f6g',
        additions: 23,
        deletions: 8
      },
      {
        id: 'c3d4e5f',
        message: 'Update API endpoints',
        author: 'Bob Wilson',
        date: new Date(Date.now() - 172800000),
        hash: 'c3d4e5f6g7h',
        additions: 67,
        deletions: 34
      }
    ]);

    setChangedFiles([
      {
        name: 'src/components/Button.js',
        status: 'modified',
        additions: 5,
        deletions: 2
      },
      {
        name: 'src/styles/theme.js',
        status: 'added',
        additions: 23,
        deletions: 0
      },
      {
        name: 'src/utils/deprecated.js',
        status: 'deleted',
        additions: 0,
        deletions: 45
      }
    ]);

    setIsClean(changedFiles.length === 0);
  };

  const handleBranchSwitch = (branchName) => {
    setCurrentBranch(branchName);
    // Simulate branch switching
    console.log(`Switching to branch: ${branchName}`);
  };

  const handleCommit = () => {
    if (!commitMessage.trim()) return;

    const newCommit = {
      id: Date.now().toString(),
      message: commitMessage,
      author: 'You',
      date: new Date(),
      hash: Math.random().toString(36).substr(2, 11),
      additions: changedFiles.reduce((sum, file) => sum + file.additions, 0),
      deletions: changedFiles.reduce((sum, file) => sum + file.deletions, 0)
    };

    setCommits(prev => [newCommit, ...prev]);
    setCommitMessage('');
    setChangedFiles([]);
    setIsClean(true);
  };

  const handlePush = () => {
    console.log('Pushing changes to remote repository...');
    // Simulate push operation
  };

  const handlePull = () => {
    console.log('Pulling changes from remote repository...');
    // Simulate pull operation
  };

  const formatDate = (date) => {
    return new Intl.RelativeTimeFormat('en', { numeric: 'auto' }).format(
      Math.ceil((date - new Date()) / (1000 * 60 * 60)),
      'hour'
    );
  };

  return (
    <GitContainer>
      <Header>
        <h1>Git Integration</h1>
        <ActionBar>
          <Button onClick={handlePull}>Pull</Button>
          <Button onClick={handlePush} disabled={isClean}>Push</Button>
          <Button>Merge</Button>
          <Button>Create Branch</Button>
        </ActionBar>
      </Header>

      <GitGrid>
        <Panel>
          <h3>Branches</h3>
          <StatusIndicator clean={isClean}>
            <div className="status-dot" />
            <span className="status-text">
              {isClean ? 'Working tree clean' : `${changedFiles.length} files changed`}
            </span>
          </StatusIndicator>
          
          <BranchList>
            <AnimatePresence>
              {branches.map(branch => (
                <BranchItem
                  key={branch.name}
                  active={branch.name === currentBranch}
                  onClick={() => handleBranchSwitch(branch.name)}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                >
                  <div className="branch-name">
                    {branch.current && '● '}
                    {branch.name}
                  </div>
                  <div className="branch-info">
                    {branch.lastCommit}
                    {(branch.ahead > 0 || branch.behind > 0) && (
                      <span>
                        {branch.ahead > 0 && ` ↑${branch.ahead}`}
                        {branch.behind > 0 && ` ↓${branch.behind}`}
                      </span>
                    )}
                  </div>
                </BranchItem>
              ))}
            </AnimatePresence>
          </BranchList>
        </Panel>

        <Panel>
          <h3>Commit History</h3>
          <CommitList>
            <AnimatePresence>
              {commits.map(commit => (
                <CommitItem
                  key={commit.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -20 }}
                >
                  <div className="commit-header">
                    <div>
                      <div className="commit-message">{commit.message}</div>
                      <div className="commit-meta">
                        {commit.author} • {formatDate(commit.date)}
                      </div>
                    </div>
                    <div className="commit-hash">{commit.hash}</div>
                  </div>
                  <div className="commit-changes">
                    <span className="additions">+{commit.additions}</span>
                    {' '}
                    <span className="deletions">-{commit.deletions}</span>
                  </div>
                </CommitItem>
              ))}
            </AnimatePresence>
          </CommitList>
        </Panel>

        <Panel>
          <h3>Changes</h3>
          <FileChanges>
            {changedFiles.map(file => (
              <div key={file.name} className="file-item">
                <span className="file-name">{file.name}</span>
                <span className={`file-status ${file.status}`}>
                  {file.status}
                </span>
              </div>
            ))}
          </FileChanges>

          {changedFiles.length > 0 && (
            <CommitForm>
              <textarea
                value={commitMessage}
                onChange={(e) => setCommitMessage(e.target.value)}
                placeholder="Enter commit message..."
              />
              <Button primary onClick={handleCommit} disabled={!commitMessage.trim()}>
                Commit Changes
              </Button>
            </CommitForm>
          )}

          {changedFiles.length === 0 && (
            <div style={{ textAlign: 'center', padding: '2rem', color: '#666' }}>
              <p>No changes to commit</p>
              <p>Your working tree is clean</p>
            </div>
          )}
        </Panel>
      </GitGrid>
    </GitContainer>
  );
};

export default GitIntegration;
