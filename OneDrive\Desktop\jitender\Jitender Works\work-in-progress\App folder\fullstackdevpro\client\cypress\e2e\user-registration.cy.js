describe('User Registration Flow', () => {
  beforeEach(() => {
    cy.visit('/register');
  });

  it('should display registration form', () => {
    cy.get('[data-testid="registration-form"]').should('be.visible');
    cy.get('input[name="email"]').should('be.visible');
    cy.get('input[name="password"]').should('be.visible');
    cy.get('input[name="confirmPassword"]').should('be.visible');
    cy.get('button[type="submit"]').should('be.visible');
  });

  it('should validate required fields', () => {
    cy.get('button[type="submit"]').click();
    
    cy.get('[data-testid="email-error"]').should('contain', 'Email is required');
    cy.get('[data-testid="password-error"]').should('contain', 'Password is required');
  });

  it('should validate email format', () => {
    cy.get('input[name="email"]').type('invalid-email');
    cy.get('input[name="password"]').type('password123');
    cy.get('input[name="confirmPassword"]').type('password123');
    cy.get('button[type="submit"]').click();
    
    cy.get('[data-testid="email-error"]').should('contain', 'Please enter a valid email');
  });

  it('should validate password confirmation', () => {
    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('password123');
    cy.get('input[name="confirmPassword"]').type('different-password');
    cy.get('button[type="submit"]').click();
    
    cy.get('[data-testid="confirm-password-error"]').should('contain', 'Passwords do not match');
  });

  it('should successfully register a new user', () => {
    cy.intercept('POST', '/api/auth/register', {
      statusCode: 201,
      body: { message: 'User registered successfully' }
    }).as('registerUser');

    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('SecurePassword123!');
    cy.get('input[name="confirmPassword"]').type('SecurePassword123!');
    cy.get('input[name="firstName"]').type('John');
    cy.get('input[name="lastName"]').type('Doe');
    cy.get('button[type="submit"]').click();

    cy.wait('@registerUser');
    cy.url().should('include', '/dashboard');
    cy.get('[data-testid="welcome-message"]').should('contain', 'Welcome, John!');
  });

  it('should handle registration errors', () => {
    cy.intercept('POST', '/api/auth/register', {
      statusCode: 400,
      body: { error: 'Email already exists' }
    }).as('registerError');

    cy.get('input[name="email"]').type('<EMAIL>');
    cy.get('input[name="password"]').type('password123');
    cy.get('input[name="confirmPassword"]').type('password123');
    cy.get('button[type="submit"]').click();

    cy.wait('@registerError');
    cy.get('[data-testid="error-message"]').should('contain', 'Email already exists');
  });

  it('should navigate to login page', () => {
    cy.get('[data-testid="login-link"]').click();
    cy.url().should('include', '/login');
  });
});
